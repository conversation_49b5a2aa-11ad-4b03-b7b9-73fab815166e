# GEOSTRATDRC Website Deployment Guide for Hostinger

## Overview
This guide will help you deploy your GEOSTRATDRC website (PHP/MySQL) to Hostinger hosting.

## Prerequisites
- Hostinger hosting account (Premium or Business plan recommended)
- Domain name (geostratdrc.org)
- FTP client (FileZilla recommended)
- Database management tool access

## Step 1: Hostinger Account Setup

### 1.1 Choose Hosting Plan
- **Recommended**: Premium Web Hosting or Business Web Hosting
- **Features needed**: PHP 8.x support, MySQL databases, SSL certificate

### 1.2 Domain Configuration
1. Purchase or transfer `geostratdrc.org` to Hostinger
2. Configure DNS settings in Hostinger control panel:
   - A Record: @ → Your server IP (auto-configured)
   - CNAME: www → geostratdrc.org
   - MX Records: For email (if using Hostinger email)

## Step 2: Database Setup

### 2.1 Create MySQL Database
1. Login to Hostinger control panel (hPanel)
2. Go to "Databases" → "MySQL Databases"
3. Create new database:
   - Database name: `geostratdrc` (or similar)
   - Username: Create new user
   - Password: Strong password
   - Note down these credentials!

### 2.2 Import Database Structure
1. Access phpMyAdmin from hPanel
2. Select your database
3. Go to "Import" tab
4. Upload `db_structure.sql` file
5. Execute import

## Step 3: File Preparation

### 3.1 Update Database Configuration
1. Copy `config/db_config_production.php` to `config/db_config.php`
2. Update with your Hostinger database credentials:
   ```php
   $db_host = 'localhost';
   $db_user = 'your_hostinger_username';
   $db_pass = 'your_hostinger_password';
   $db_name = 'your_hostinger_database_name';
   ```

### 3.2 Update Site Settings
Update the following in your database `site_settings` table:
- `contact_email`: Your actual email
- `contact_phone`: Your actual phone
- `contact_address`: Your actual address
- Social media URLs
- Google Analytics ID (if you have one)

## Step 4: File Upload

### 4.1 FTP Upload
1. Get FTP credentials from Hostinger control panel
2. Use FileZilla or similar FTP client
3. Upload all files to `public_html` directory
4. Ensure proper file permissions:
   - Folders: 755
   - PHP files: 644
   - Images: 644

### 4.2 File Structure on Server
```
public_html/
├── admin/
├── config/
├── css/
├── database/
├── img/
├── includes/
├── js/
├── lib/
├── scss/
├── .htaccess
├── index.html
├── about.html
├── contact.html
├── contact.php
└── [other files...]
```

## Step 5: SSL Certificate Setup

### 5.1 Enable SSL
1. In Hostinger control panel, go to "SSL"
2. Enable "Free SSL Certificate"
3. Wait for activation (usually 15-30 minutes)

### 5.2 Force HTTPS
Add to your `.htaccess` file:
```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## Step 6: Email Configuration

### 6.1 Create Email Accounts
1. Go to "Email" in Hostinger control panel
2. Create email accounts:
   - <EMAIL>
   - <EMAIL>

### 6.2 Update Mail Configuration
Update `config/mail_config.php` with Hostinger SMTP settings:
```php
$smtp_host = 'smtp.hostinger.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'your_email_password';
```

## Step 7: Testing

### 7.1 Website Functionality
Test the following:
- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] Contact form submits successfully
- [ ] Newsletter subscription works
- [ ] Admin panel access (if applicable)
- [ ] Database connections work

### 7.2 Performance Testing
- Check page load speeds
- Test on mobile devices
- Verify SSL certificate is working

## Step 8: Post-Deployment Tasks

### 8.1 SEO Setup
1. Submit sitemap to Google Search Console
2. Set up Google Analytics (update tracking ID in database)
3. Verify social media meta tags

### 8.2 Backup Setup
1. Enable automatic backups in Hostinger
2. Set up regular database backups
3. Consider using a backup plugin

### 8.3 Security
1. Change default admin passwords
2. Update PHP to latest version
3. Enable security headers in .htaccess
4. Regular security updates

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check credentials in `db_config.php`
2. **File permission errors**: Set correct permissions (755 for folders, 644 for files)
3. **Email not working**: Verify SMTP settings and email account setup
4. **SSL issues**: Wait for SSL activation, check .htaccess redirects

### Support Resources
- Hostinger Knowledge Base
- Hostinger Live Chat Support
- Community forums

## Maintenance

### Regular Tasks
- Update PHP version when available
- Monitor website performance
- Check for broken links
- Update content regularly
- Monitor security logs

### Backup Schedule
- Daily: Database backups
- Weekly: Full file backups
- Monthly: Download local copies

## Contact Information
For technical support with this deployment, contact your development team or Hostinger support.

---
**Note**: Replace placeholder values with your actual Hostinger account details before deployment.
