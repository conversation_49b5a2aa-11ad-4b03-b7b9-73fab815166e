<?php
use <PERSON><PERSON><PERSON><PERSON>er\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'vendor/autoload.php';

class MailService {
    private $mailer;

    public function __construct() {
        $this->mailer = new PHPMailer(true);
        
        // Configuration du serveur SMTP
        $this->mailer->isSMTP();
        $this->mailer->Host = 'smtp.geostratdrc.org';
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = '<EMAIL>';
        $this->mailer->Password = 'your_smtp_password';
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->mailer->Port = 587;
        
        // Configuration par défaut
        $this->mailer->setFrom('<EMAIL>', 'GEOSTRATDRC');
        $this->mailer->CharSet = 'UTF-8';
    }

    public function sendNotification($to, $subject, $body) {
        try {
            $this->mailer->addAddress($to);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
}