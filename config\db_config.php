<?php
// Informations de connexion à la base de données
$db_host = 'localhost';
$db_user = 'root';  // À modifier selon votre configuration
$db_pass = '';      // À modifier selon votre configuration
$db_name = 'geostratdrc';

// Créer la connexion
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Vérifier la connexion
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Définir le jeu de caractères
$conn->set_charset("utf8mb4");

// Fonction pour échapper les chaînes de caractères
function escape($conn, $string) {
    return $conn->real_escape_string($string);
}

// Fonction pour générer un slug à partir d'une chaîne
function generateSlug($string) {
    // Remplacer les caractères non alphanumériques par des tirets
    $slug = preg_replace('/[^a-z0-9]+/i', '-', strtolower(trim($string)));
    // Supprimer les tirets en début et fin de chaîne
    $slug = trim($slug, '-');
    return $slug;
}

// Fonction pour formater la date
function formatDate($dateString, $format = 'd F Y') {
    $date = new DateTime($dateString);
    return $date->format($format);
}

// Fonction pour tronquer un texte
function truncateText($text, $length = 100, $append = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $text = substr($text, 0, $length);
    $text = substr($text, 0, strrpos($text, ' '));
    
    return $text . $append;
}

// Fonction pour nettoyer les balises HTML indésirables
function sanitizeHTML($html) {
    // Configuration de HTML Purifier
    require_once 'lib/htmlpurifier/HTMLPurifier.auto.php';
    $config = HTMLPurifier_Config::createDefault();
    $config->set('HTML.Allowed', 'p,b,i,strong,em,a[href|title],ul,ol,li,br,span,img[src|alt|width|height],h1,h2,h3,h4,h5,h6,blockquote');
    $purifier = new HTMLPurifier($config);
    
    return $purifier->purify($html);
}

// Fonction pour vérifier si l'utilisateur est connecté
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Fonction pour vérifier si l'utilisateur est administrateur
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Fonction pour obtenir les paramètres du site
function getSiteSettings($conn, $key = null) {
    if ($key) {
        $stmt = $conn->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return $row['setting_value'];
        }
        
        return null;
    } else {
        $settings = [];
        $result = $conn->query("SELECT setting_key, setting_value FROM site_settings");
        
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
}
?>

