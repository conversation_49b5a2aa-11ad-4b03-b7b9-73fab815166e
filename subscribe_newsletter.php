<?php
require_once 'config/db_config.php';
require_once 'includes/spam_protection.php';

// Initialiser la protection contre le spam
$spamProtection = new SpamProtection($conn);

// Vérifier si la requête est de type POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Vérifier si l'IP n'est pas bloquée pour spam
    if (!$spamProtection->checkRequest()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => "Too many requests. Please try again later."
        ]);
        exit;
    }
    
    // Récupérer et nettoyer les données
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $name = isset($_POST['name']) ? trim($_POST['name']) : null;
    
    // Validation de base
    $errors = [];
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    // Réponse en JSON
    header('Content-Type: application/json');
    
    // S'il y a des erreurs, renvoyer un message d'erreur
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => implode(', ', $errors)
        ]);
        exit;
    }
    
    // Vérifier si l'email existe déjà
    $check_stmt = $conn->prepare("SELECT id, status FROM newsletter_subscribers WHERE email = ?");
    $check_stmt->bind_param("s", $email);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        $subscriber = $check_result->fetch_assoc();
        
        // Si l'abonné est déjà actif
        if ($subscriber['status'] === 'active') {
            echo json_encode([
                'success' => false,
                'message' => "This email is already subscribed to our newsletter."
            ]);
            exit;
        } else {
            // Réactiver l'abonnement
            $stmt = $conn->prepare("UPDATE newsletter_subscribers SET status = 'active', name = COALESCE(?, name) WHERE id = ?");
            $stmt->bind_param("si", $name, $subscriber['id']);
            
            if ($stmt->execute()) {
                echo json_encode([
                    'success' => true,
                    'message' => "You have successfully resubscribed to our newsletter."
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => "Error updating subscription: " . $conn->error
                ]);
            }
            exit;
        }
    }
    
    // Insérer le nouvel abonné dans la base de données
    $stmt = $conn->prepare("INSERT INTO newsletter_subscribers (email, name, status) VALUES (?, ?, 'active')");
    $stmt->bind_param("ss", $email, $name);
    
    if ($stmt->execute()) {
        // Envoyer un email de confirmation
        $to = $email;
        $subject = "Newsletter Subscription Confirmation";
        $message = "Dear " . ($name ? $name : "Subscriber") . ",\n\n";
        $message .= "Thank you for subscribing to the GEOSTRATDRC newsletter. You will now receive updates on our latest articles, projects, and events.\n\n";
        $message .= "If you did not subscribe to our newsletter, please ignore this email or contact <NAME_EMAIL>.\n\n";
        $message .= "Best regards,\n";
        $message .= "The GEOSTRATDRC Team";
        
        $headers = "From: <EMAIL>";
        
        mail($to, $subject, $message, $headers);
        
        echo json_encode([
            'success' => true,
            'message' => "Thank you for subscribing to our newsletter!"
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => "Error saving subscription: " . $conn->error
        ]);
    }
} else {
    // Si la méthode n'est pas POST, rediriger vers la page d'accueil
    header('Location: index.html');
    exit;
}
?>