-- Table des administrateurs
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Table des logs d'administration
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id)
);

-- Table pour le rate limiting
CREATE TABLE rate_limits (
    ip_address VARCHAR(45) PRIMARY KEY,
    request_count INT DEFAULT 1,
    first_request TIMESTA<PERSON> DEFAULT CURRENT_TIMESTAMP,
    last_request TIM<PERSON><PERSON>MP DEFAULT CURRENT_TIMESTAMP
);