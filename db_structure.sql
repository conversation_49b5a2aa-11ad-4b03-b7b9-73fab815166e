-- Structure de la table des catégories de blog
CREATE TABLE IF NOT EXISTS `blog_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table des articles de blog
CREATE TABLE IF NOT EXISTS `blog_posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `excerpt` text,
  `image` varchar(255) DEFAULT NULL,
  `author` varchar(100) NOT NULL,
  `category_id` int(11) NOT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `status` enum('published','draft','archived') NOT NULL DEFAULT 'published',
  `meta_description` varchar(255) DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `blog_posts_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table des utilisateurs
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `role` enum('admin','author','subscriber') NOT NULL DEFAULT 'subscriber',
  `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table des commentaires
CREATE TABLE IF NOT EXISTS `comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_name` varchar(100) DEFAULT NULL,
  `user_email` varchar(100) DEFAULT NULL,
  `content` text NOT NULL,
  `status` enum('pending','approved','rejected','spam') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `blog_posts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Données d'exemple pour les catégories
INSERT INTO `blog_categories` (`name`, `slug`, `description`) VALUES
('Remote Sensing', 'remote-sensing', 'Articles about satellite imagery and remote sensing applications'),
('GIS', 'gis', 'Geographic Information Systems and spatial analysis'),
('Climate Change', 'climate-change', 'Climate change impacts and adaptation strategies'),
('Agriculture', 'agriculture', 'Sustainable and climate-smart agriculture'),
('Urban Planning', 'urban-planning', 'Sustainable urban development and planning'),
('Conservation', 'conservation', 'Biodiversity and natural resource conservation');

-- Données d'exemple pour les articles (à adapter selon vos besoins)
INSERT INTO `blog_posts` (`title`, `slug`, `content`, `excerpt`, `image`, `author`, `category_id`, `tags`, `status`, `meta_description`, `meta_keywords`, `created_at`) VALUES
('Satellite Monitoring Reveals Deforestation Trends in Congo Basin', 'satellite-monitoring-deforestation-congo', '<p>The Congo Basin, home to the world\'s second-largest tropical rainforest, faces significant deforestation pressures. Our recent analysis using Sentinel-2 and Landsat imagery reveals concerning patterns of forest loss across key regions of the Democratic Republic of Congo.</p><p>Using advanced change detection algorithms and time-series analysis, our team identified hotspots of deforestation primarily driven by agricultural expansion, artisanal logging, and mining activities. The study covered a five-year period from 2018 to 2023, revealing an average annual forest loss of approximately 2.3% in monitored areas.</p><p>Particularly concerning are the accelerating rates of forest fragmentation, which threatens biodiversity and ecosystem services beyond the directly deforested areas. Our spatial analysis indicates that forest fragmentation has increased by 15% over the study period, creating isolated forest patches that are more vulnerable to edge effects and further degradation.</p><p>The findings underscore the urgent need for enhanced monitoring systems and targeted conservation interventions. Through partnerships with local communities and conservation organizations, we\'re developing early warning systems that can detect forest disturbances in near-real-time, enabling more rapid responses to illegal deforestation activities.</p>', 'New satellite analysis reveals concerning patterns of deforestation and forest fragmentation across the Congo Basin, with implications for biodiversity conservation and climate change mitigation efforts.', 'img/blog/deforestation.jpg', 'Dr. Jean Kabongo', 1, 'deforestation,remote sensing,forest monitoring,conservation', 'published', 'Satellite monitoring reveals accelerating deforestation trends in the Congo Basin, with implications for biodiversity and climate.', 'deforestation, Congo Basin, satellite monitoring, forest loss, conservation', '2023-06-15 08:30:00'),

('GIS Applications for Urban Planning in Rapidly Growing African Cities', 'gis-urban-planning-african-cities', '<p>Rapid urbanization across Africa presents both challenges and opportunities for sustainable development. Our recent work implementing GIS-based planning tools in several growing cities demonstrates the transformative potential of spatial technologies for urban management.</p><p>In Bukavu, we developed a comprehensive spatial database integrating infrastructure networks, land use patterns, and demographic data. This integrated approach enabled city planners to identify service provision gaps and prioritize infrastructure investments in underserved areas. Particularly valuable was the analysis of informal settlement patterns and their relationship to environmental hazards such as flooding and landslides.</p><p>The implementation of web-based GIS platforms has democratized access to spatial information, allowing various stakeholders including community organizations to participate more effectively in planning processes. Interactive mapping tools have facilitated public consultations on new development projects, increasing transparency and community buy-in.</p><p>Key to the success of these initiatives has been the capacity building component, with over 50 local officials and technicians trained in GIS data collection, management, and analysis. This focus on local capacity ensures the sustainability of the systems beyond initial project timeframes.</p>', 'Innovative GIS applications are transforming urban planning processes in rapidly growing African cities, enabling more inclusive and sustainable development through improved spatial data management and analysis.', 'img/blog/urban-planning.jpg', 'Esther Mutombo', 2, 'GIS,urban planning,smart cities,sustainable development', 'published', 'How GIS technologies are revolutionizing urban planning in rapidly growing African cities through improved spatial data management and analysis.', 'GIS, urban planning, African cities, spatial analysis, sustainable development', '2023-07-22 10:15:00'),

('Climate-Smart Agriculture: Adapting to Changing Rainfall Patterns in Eastern DRC', 'climate-smart-agriculture-rainfall-patterns', '<p>Changing rainfall patterns in Eastern DRC are significantly impacting agricultural productivity, threatening food security for millions of smallholder farmers. Our recent field research combined with climate data analysis reveals shifting precipitation trends, with more intense rainfall events and longer dry spells disrupting traditional farming calendars.</p><p>Working with farming communities in South Kivu province, we\'ve implemented a suite of climate-smart agricultural practices adapted to local conditions. These include drought-resistant crop varieties, improved water harvesting techniques, and agroforestry systems that enhance resilience to climate variability.</p><p>Particularly promising has been the integration of satellite-derived rainfall estimates with local weather station data to develop more accurate seasonal forecasts. These forecasts, disseminated through mobile phones and community radio, help farmers make more informed decisions about planting times and crop selection.</p><p>Economic analysis of the implemented practices shows significant benefits, with participating farmers experiencing 30-45% higher yields during drought conditions compared to those using conventional methods. The diversification of crop systems has also reduced vulnerability to both climate and market shocks.</p>', 'Innovative climate-smart agricultural practices are helping farmers in Eastern DRC adapt to changing rainfall patterns, combining traditional knowledge with satellite data and improved forecasting to enhance food security.', 'img/blog/agriculture.jpg', 'Prof. Marie Kanyange', 3, 'climate change,agriculture,food security,adaptation', 'published', 'How climate-smart agricultural practices are helping farmers in Eastern DRC adapt to changing rainfall patterns and enhance food security.', 'climate-smart agriculture, rainfall patterns, DRC, adaptation, food security', '2023-08-10 14:45:00'),

('Mapping Mineral Resources: Balancing Economic Development and Environmental Protection', 'mapping-mineral-resources-development-environment', '<p>The Democratic Republic of Congo\'s vast mineral wealth represents both an economic opportunity and an environmental challenge. Our recent geological mapping project, combining remote sensing techniques with field surveys, has produced the most detailed mineral resource maps to date for several key provinces.</p><p>Using advanced spectral analysis of multispectral and hyperspectral satellite imagery, we\'ve identified previously unknown mineral deposits while also mapping the environmental impacts of existing mining operations. The integration of these datasets with hydrological models has been particularly valuable for understanding potential contamination pathways and vulnerable ecosystems.</p><p>A key innovation in our approach has been the development of environmental sensitivity indices that help prioritize areas where mining activities should be restricted or subject to enhanced safeguards. These indices incorporate biodiversity values, ecosystem services, and community dependencies on natural resources.</p><p>The project has engaged multiple stakeholders, including government agencies, mining companies, and local communities, fostering dialogue about responsible resource development. Training programs for environmental monitoring have empowered communities to better understand and document impacts from nearby operations.</p>', 'Advanced geological mapping techniques are helping balance mineral resource development with environmental protection in the DRC, creating more detailed resource maps while identifying sensitive ecosystems requiring protection.', 'img/blog/mining.jpg', 'Dr. Patrick Muamba', 2, 'mining,mineral resources,environmental protection,remote sensing', 'published', 'How advanced geological mapping techniques are helping balance mineral resource development with environmental protection in the DRC.', 'mineral resources, mapping, DRC, environmental protection, sustainable mining', '2023-09-05 11:20:00'),

('Biodiversity Monitoring Using eDNA: New Insights from Congo River Basin', 'biodiversity-monitoring-edna-congo-river', '<p>Traditional biodiversity surveys in the Congo River Basin face significant challenges due to remote terrain and the cryptic nature of many species. Our pioneering work using environmental DNA (eDNA) sampling is revolutionizing our understanding of aquatic biodiversity in this globally important ecosystem.</p><p>By collecting and analyzing water samples from various locations along the river system, we\'ve detected over 300 fish species, including several previously undocumented in particular sub-basins. The technique has proven especially valuable for monitoring rare and endangered species that are difficult to detect using conventional survey methods.</p><p>Spatial analysis of the eDNA results has revealed important biodiversity hotspots that warrant enhanced conservation attention. Particularly significant are the small tributary systems that serve as critical breeding habitats for many species but have received less protection than main river channels.</p><p>The project has built local capacity in molecular techniques, with a new laboratory established at the University of Kinshasa now processing samples that previously required international shipping. This development significantly reduces costs and sample degradation issues, while building sustainable scientific infrastructure within the DRC.</p>', 'Environmental DNA sampling is revolutionizing biodiversity monitoring in the Congo River Basin, detecting hundreds of fish species including previously undocumented ones and identifying critical conservation areas.', 'img/blog/biodiversity.jpg', 'Dr. Aimée Lokonda', 6, 'biodiversity,conservation,eDNA,aquatic ecosystems', 'published', 'How environmental DNA sampling is revolutionizing biodiversity monitoring in the Congo River Basin and informing conservation priorities.', 'biodiversity, eDNA, Congo River, conservation, aquatic ecosystems', '2023-10-12 09:40:00'),

('Urban Heat Islands: Satellite Analysis Reveals Temperature Patterns in Growing Cities', 'urban-heat-islands-satellite-analysis', '<p>As African cities rapidly expand, urban heat island effects are intensifying, with significant implications for public health and energy consumption. Our recent thermal remote sensing study of major cities in Central Africa reveals temperature differentials of up to 7°C between urban centers and surrounding rural areas during peak heat periods.</p><p>Using Landsat 8 and ECOSTRESS thermal data, we\'ve created high-resolution urban heat maps that identify the most vulnerable neighborhoods. Spatial analysis reveals strong correlations between heat intensity, vegetation cover, and building density, with informal settlements often experiencing the most extreme conditions due to dense construction and limited green space.</p><p>Temporal analysis shows concerning trends, with urban-rural temperature differentials increasing by approximately 0.5°C per decade as cities expand and densify. These findings have significant implications for urban planning, particularly as climate change brings more frequent and intense heat waves to the region.</p><p>Working with city authorities, we\'ve developed heat vulnerability indices that combine physical heat exposure with socioeconomic vulnerability factors. These indices are now informing targeted interventions such as urban greening programs, cool roof initiatives, and the establishment of cooling centers in high-risk areas.</p>', 'Satellite thermal analysis reveals intensifying urban heat island effects in rapidly growing African cities, with temperature differentials of up to 7°C between urban centers and rural areas, highlighting the need for heat-conscious urban planning.', 'img/blog/urban-heat.jpg', 'Dr. Claude Muhindo', 5, 'urban heat islands,climate change,remote sensing,urban planning', 'published', 'How satellite thermal analysis is revealing concerning urban heat island patterns in rapidly growing African cities and informing adaptation strategies.', 'urban heat islands, thermal remote sensing, African cities, climate adaptation, urban planning', '2023-11-08 13:25:00');

-- Structure de la table des messages de contact
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('new','read','replied') NOT NULL DEFAULT 'new',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour la protection contre le spam
CREATE TABLE IF NOT EXISTS `rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `request_count` int(11) NOT NULL DEFAULT '1',
  `first_request` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_request` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les abonnés à la newsletter
CREATE TABLE IF NOT EXISTS `newsletter_subscribers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `status` enum('active','unsubscribed') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les projets
CREATE TABLE IF NOT EXISTS `projects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `client` varchar(100) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('ongoing','completed','planned') NOT NULL DEFAULT 'ongoing',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les services
CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les témoignages
CREATE TABLE IF NOT EXISTS `testimonials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) DEFAULT NULL,
  `organization` varchar(100) DEFAULT NULL,
  `content` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les membres de l'équipe
CREATE TABLE IF NOT EXISTS `team_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) NOT NULL,
  `bio` text,
  `image` varchar(255) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `linkedin` varchar(255) DEFAULT NULL,
  `twitter` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Structure de la table pour les paramètres du site
CREATE TABLE IF NOT EXISTS `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insérer des paramètres de site par défaut
INSERT INTO `site_settings` (`setting_key`, `setting_value`, `setting_description`) VALUES
('site_title', 'GEOSTRATDRC - Geospatial Solutions for Sustainable Development', 'Site title used in browser tabs and SEO'),
('contact_email', '<EMAIL>', 'Primary contact email address'),
('contact_phone', '+243973583690', 'Primary contact phone number'),
('contact_address', '123 Street, Bukavu, Sud-Kivu, DRC', 'Physical office address'),
('social_facebook', 'https://facebook.com/geostratdrc', 'Facebook page URL'),
('social_twitter', 'https://twitter.com/geostratdrc', 'Twitter profile URL'),
('social_linkedin', 'https://linkedin.com/company/geostratdrc', 'LinkedIn company page URL'),
('social_instagram', 'https://instagram.com/geostratdrc', 'Instagram profile URL'),
('footer_text', '© GEOSTRATDRC, All Rights Reserved. Pioneering Geospatial Excellence in Central Africa', 'Text displayed in the website footer'),
('google_analytics', 'UA-XXXXXXXXX-X', 'Google Analytics tracking ID');
