<?php
require_once 'config/db_config.php';

// Vérifier si la requête est de type POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Récupérer et nettoyer les données
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $comment = isset($_POST['comment']) ? trim($_POST['comment']) : '';
    
    // Validation de base
    $errors = [];
    
    if (empty($post_id)) {
        $errors[] = "Invalid post ID";
    }
    
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($comment)) {
        $errors[] = "Comment is required";
    }
    
    // Réponse en JSON
    header('Content-Type: application/json');
    
    // S'il y a des erreurs, renvoyer un message d'erreur
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => implode(', ', $errors)
        ]);
        exit;
    }
    
    // Vérifier si l'article existe
    $check_stmt = $conn->prepare("SELECT id FROM blog_posts WHERE id = ?");
    $check_stmt->bind_param("i", $post_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => "The post does not exist"
        ]);
        exit;
    }
    
    // Insérer le commentaire dans la base de données
    $stmt = $conn->prepare("INSERT INTO comments (post_id, user_name, user_email, content, status, created_at) VALUES (?, ?, ?, ?, 'pending', NOW())");
    $stmt->bind_param("isss", $post_id, $name, $email, $comment);
    
    if ($stmt->execute()) {
        // Envoyer une notification par email à l'administrateur
        $admin_email = "<EMAIL>";
        $subject = "New comment awaiting approval";
        $message = "A new comment has been submitted on your blog and is awaiting approval.\n\n";
        $message .= "Post ID: $post_id\n";
        $message .= "Name: $name\n";
        $message .= "Email: $email\n";
        $message .= "Comment: $comment\n\n";
        $message .= "To approve or reject this comment, please log in to your admin panel.";
        
        $headers = "From: <EMAIL>";
        
        mail($admin_email, $subject, $message, $headers);
        
        echo json_encode([
            'success' => true,
            'message' => "Your comment has been submitted and is awaiting approval."
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => "Error saving comment: " . $conn->error
        ]);
    }
} else {
    // Si la méthode n'est pas POST, rediriger vers la page d'accueil
    header('Location: index.html');
    exit;
}
?>