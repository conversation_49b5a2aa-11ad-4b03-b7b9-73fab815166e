<?php
/**
 * Classe pour la protection contre le spam
 * Limite le nombre de requêtes par IP pour éviter les abus
 */
class SpamProtection {
    private $conn;
    private $max_requests = 10; // Nombre maximum de requêtes autorisées
    private $time_window = 3600; // Fenêtre de temps en secondes (1 heure)
    
    /**
     * Constructeur
     * @param mysqli $conn Connexion à la base de données
     */
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Vérifie si l'IP actuelle a dépassé la limite de requêtes
     * @return bool True si l'IP est autorisée, False si elle est bloquée
     */
    public function checkRequest() {
        $ip = $this->getIpAddress();
        
        // Nettoyer les anciennes entrées
        $this->cleanOldEntries();
        
        // Vérifier si l'IP existe déjà
        $stmt = $this->conn->prepare("SELECT * FROM rate_limits WHERE ip_address = ?");
        $stmt->bind_param("s", $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $request_count = $row['request_count'];
            $first_request = strtotime($row['first_request']);
            $current_time = time();
            
            // Si la première requête est dans la fenêtre de temps
            if (($current_time - $first_request) < $this->time_window) {
                // Si le nombre de requêtes dépasse la limite
                if ($request_count >= $this->max_requests) {
                    return false; // Bloquer la requête
                } else {
                    // Incrémenter le compteur
                    $stmt = $this->conn->prepare("UPDATE rate_limits SET request_count = request_count + 1, last_request = NOW() WHERE ip_address = ?");
                    $stmt->bind_param("s", $ip);
                    $stmt->execute();
                    return true; // Autoriser la requête
                }
            } else {
                // Réinitialiser le compteur si la fenêtre de temps est dépassée
                $stmt = $this->conn->prepare("UPDATE rate_limits SET request_count = 1, first_request = NOW(), last_request = NOW() WHERE ip_address = ?");
                $stmt->bind_param("s", $ip);
                $stmt->execute();
                return true; // Autoriser la requête
            }
        } else {
            // Nouvelle IP, ajouter à la table
            $stmt = $this->conn->prepare("INSERT INTO rate_limits (ip_address, request_count, first_request, last_request) VALUES (?, 1, NOW(), NOW())");
            $stmt->bind_param("s", $ip);
            $stmt->execute();
            return true; // Autoriser la requête
        }
    }
    
    /**
     * Nettoie les entrées plus anciennes que la fenêtre de temps
     */
    private function cleanOldEntries() {
        $time_limit = date('Y-m-d H:i:s', time() - $this->time_window);
        $this->conn->query("DELETE FROM rate_limits WHERE last_request < '$time_limit'");
    }
    
    /**
     * Obtient l'adresse IP du visiteur
     * @return string Adresse IP
     */
    private function getIpAddress() {
        // Vérifier différentes variables serveur pour obtenir l'IP réelle derrière un proxy
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        // Nettoyer l'IP pour éviter les injections
        return filter_var($ip, FILTER_VALIDATE_IP);
    }
    
    /**
     * Définit le nombre maximum de requêtes autorisées
     * @param int $max Nombre maximum de requêtes
     */
    public function setMaxRequests($max) {
        $this->max_requests = $max;
    }
    
    /**
     * Définit la fenêtre de temps en secondes
     * @param int $seconds Fenêtre de temps en secondes
     */
    public function setTimeWindow($seconds) {
        $this->time_window = $seconds;
    }
}
?>

